<?php
declare(strict_types=1);

use MailScanModule\Emailers\MailboxEmailer;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;

final class CreateMailboxFreeTrialEmail extends AbstractMigration
{
    public function up(): void
    {
        if ($this->getNodeId() !== null) return;

        $page = new Page('Mailbox Free Trial Email');

        $properties = [
            new Property('templateName', MailboxEmailer::MAILBOX_FREE_TRIAL_EMAIL),
            new Property('subject', MailboxEmailer::MAILBOX_FREE_TRIAL_EMAIL_SUBJECT),
            new Property('from', '<EMAIL>'),
            new Property('fromName', 'Companies Made Simple'),
            new Property('tag1', 'mailbox'),
            new Property('tag2', 'mail'),
            new Property('tag3', 'trial'),
            new Property('tag4', 'mail-forwarding'),
        ];

        $node = new Node(
            $page,
            $properties,
            MailboxEmailer::MAILBOX_FREE_TRIAL_EMAIL,
            137,
            null,
            'EmailAdminControler',
            300
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    public function down(): void
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId(): ?string
    {
        $node = $this->fetchRow(sprintf("SELECT `node_id` FROM `%s` WHERE `name`='%s'", TBL_NODES, MailboxEmailer::MAILBOX_FREE_TRIAL_EMAIL));
        return $node['node_id'] ?? null;
    }
}
