.PHONY: help clean cs-fixer

# the following 3 lines are critical - do not alter or remove them!
# makes entire target/recipe execute within a single bash process.
.ONESHELL:
export SHELL := /bin/bash
export SHELLOPTS := $(if $(SHELLOPTS),$(SHELLOPTS):)errexit:pipefail

# make docker use buildkit, and make docker-compose use docker cli, for support of multi-stage builds from Dockerfile
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

help:
	@: noop to silence command echoing
	printf "Usage:\n make [target]\n\nAvailable targets:\n"
	awk '/^[a-zA-Z\-\_0-9\.@]+:/ { \
		if (helpMessage = match(lastLine, /^## (.*)/)) { \
			printf " %-48s$ %s\n", substr($$1, 0, index($$1, ":")), substr(lastLine, RSTART + 3, RLENGTH); \
		} \
	} { lastLine = $$0 }' $(MAKEFILE_LIST)

clean:
	docker-compose down --rmi local --volumes || true


cs-fixer:
	./vendor/bin/php-cs-fixer fix project/MailScanModule/Services/PostItemService.php --config=.php-cs-fixer.dist.php --verbose

phpstan:
	./vendor/bin/phpstan analyse --configuration=./phpstan.neon