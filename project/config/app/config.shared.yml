imports:
  - { resource: "%root%/project/config/experiments.yml" }
  - { resource: "%root%/project/config/features.yml" }
  - { resource: "%root%/project/config/app/menu.yml"}
  - { resource: "%root%/project/PaymentModule/Config/config.yml" }
  - { resource: "%root%/project/UserModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/user_module/src/UserModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/form_module/src/FormModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/payment_module/src/PaymentModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/sagepay_rest_api/src/Config/config.yml" }
  - { resource: "%root%/project/CustomerModule/Config/config.yml" }
  - { resource: "%root%/project/CompanyModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/workflow_engine_module/src/WorkflowEngineModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/utils/src/BasketModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/utils/src/NotificationModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/router_module/src/RouterModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/feature_module/src/Config/config.yml" }
  - { resource: "%root%/project/CompanyFormationModule/Config/config.yml" }
  - { resource: "%root%/project/BasketModule/Config/config.yml" }
  - { resource: "%root%/project/IdCheckModule/Config/config.yml" }
  - { resource: "%root%/project/HomeModule/Config/config.yml" }
  - { resource: "%root%/project/PackageModule/Config/config.yml" }
  - { resource: "%root%/project/NotificationModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/error_module/src/ErrorModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/console/src/Console/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/cron/src/Cron/Config/config.yml" }
  - { resource: "%root%/project/BankingModule/Config/config.yml" }
  - { resource: "%root%/project/BusinessServicesModule/Config/config.yml" }
  - { resource: "%root%/project/TemplatingModule/Config/config.yml" }
  - { resource: "%root%/project/MailScanModule/Config/config.yml" }
  - { resource: "%root%/project/RenewalModule/Config/config.yml" }
  - { resource: "%root%/project/LoginModule/Config/config.yml" }
  - { resource: "%root%/project/LandingPagesModule/Config/config.yml" }
  - { resource: "%root%/project/PagesModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/companies-house/src/CompaniesHouse/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/http-client/src/HttpClient/Config/config.yml" }
  - { resource: "%root%/project/CompanySyncModule/Config/config.yml" }
  - { resource: "%root%/project/OfferModule/Config/config.yml" }
  - { resource: "%root%/project/PeopleWithSignificantControlModule/Config/config.yml" }
  - { resource: "%root%/project/CompaniesHouseModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/content_module/src/ContentModule/Config/config.yml" }
  - { resource: "%root%/project/AdminModule/Config/config.yml" }
  - { resource: "%root%/project/MyServicesModule/Config/config.yml" }
  - { resource: "%root%/project/ServiceCancellationModule/Config/config.yml" }
  - { resource: "%root%/project/ServiceRemindersModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/id_module/src/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/storage_module/src/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/utils/src/TranslationModule/Config/config.yml" }
  - { resource: "%root%/project/CashplusModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/cache_module/src/CacheModule/Config/config.yml" }
  - { resource: "%root%/project/ServiceModule/Config/config.yml" }
  - { resource: "%root%/project/MarketingModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/utils/src/OrmModule/Config/config.yml" }
  - { resource: "%root%/project/EmailModule/Config/config.yml" }
  - { resource: "%root%/project/CashBackModule/Config/config.yml" }
  - { resource: "%root%/project/ProductModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/utils/src/ArchiveModule/Config/config.yml" }
  - { resource: "%root%/project/BusinessDataModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/cross_site_module/src/Config/config.yml" }
  - { resource: "%root%/project/FeedbackModule/Config/config.yml" }
  - { resource: "%root%/project/FormModule/Config/config.yml" }
  - { resource: "%root%/vendor/made_simple/msg-framework/experiment_module/src/Config/config.yml" }
  - { resource: "%root%/project/FrontModule/Config/config.yml" }
  - { resource: "%root%/project/config/servicesWidget.yml" }
  - { resource: "%root%/project/CsmsModule/Config/config.yml" }
  - { resource: "%root%/project/NotificationModule/Config/notificationMapper.yml" }
  - { resource: "%root%/project/DynamicTemplateModule/Config/config.yml" }
  - { resource: "%root%/project/BigQueryModule/Config/config.yml" }
  - { resource: "%root%/project/MoneyPennyNumbersModule/Config/config.yml" }
  - { resource: "%root%/project/LoggableModule/Config/config.yml" }
  - { resource: "%root%/project/OmnipayModule/Config/config.yml" }
  - { resource: "%root%/project/CompanyMonitoringModule/Config/config.yml" }
  - { resource: "%root%/project/GhostModule/Config/config.yml" }
  - { resource: "%root%/project/FirebaseModule/Config/config.yml" }
  - { resource: "%root%/project/SendInBlueModule/Config/config.yml" }
  - { resource: "%root%/project/EventDetailModule/Config/config.yml" }
  - { resource: "%root%/project/CompanyDeleteModule/Config/config.yml" }
  - { resource: "%root%/project/MantleModule/Config/config.yml" }
  - { resource: "%root%/project/StrapiModule/Config/config.yml" }
  - { resource: "%root%/project/SentryModule/Config/config.yml" }
  - { resource: "%root%/project/GoogleAnalyticsModule/Config/config.yml" }
  - { resource: "%root%/project/CompanyIncorporationModule/Config/config.yml" }
  - { resource: "%root%/project/PubSubModule/Config/config.yml" }
  - { resource: "%root%/project/PayByPhoneModule/Config/config.yml" }
  - { resource: "%root%/project/Commands/Config/config.yml" }

projectName: Companies Made Simple
author: Stanislav Bazik, Peter Reisinger
copyright: Stanislav Bazik
admin_email:  <EMAIL>
allowed_lngs:
  - EN
default_lng: EN
friendly_url: true
home_node: 44
env_variables_prefix: CMS__

# clarity
show_clarity: true

# sentry
show_sentry: true

# image thumbnails
thumbs:
  s:
    dir: system
    width: 150
    height: 113
  journey:
    dir: journey
    width: 78
    height: 74

# console ssh
console:
  ssh:
    projectName: cms
    projectDirectory: /var/www/html/companiesmadesimple.com/html
    ip: **************
    user: developer
  sync:
    database:
      database: cms
      host: **************
      username: reader
      password:
    tables:
      - cms2_properties
      - cms2_nodes
      - cms2_pages
      - cms2_users
      - cms2_users_roles

api:
  header-auth-name: 'X-Msg-Auth'
  auth-token: 'KjsjkaLK8982kkjj8712'
  cors:
    headers: '*'
    origin: '*'
    methods: 'GET, POST, PUT, OPTIONS'
    credentials: 'true'

payment:
  currency: GBP
  description: Companies Made Simple
  3dauth_redirect: true

emails:
  from:
    <EMAIL>: Companies Made Simple
  from_name: Companies Made Simple
  attachment_path: "%root%/storage/attachments"

mailgun:
  smtp:
    host: smtp.mailgun.org
    username:
    password:
    secure: ssl
  apikey: ''
  domains:
    cms: companiesmadesimple.com
    marketing: email.companiesmadesimple.com
    tsb_business: tsbbusiness.co.uk
    cool: mg.msg.cool
  authorizedRecipients: []

database:
  host: localhost
  username:
  password:
  database:
  lazy: TRUE
  driver: mysqli

vo_database:
  host: localhost
  username:
  password:
  database:
  lazy: TRUE
  driver: mysqli

cms_mirror_database:
  host: localhost
  username:
  password:
  database:
  lazy: TRUE
  driver: mysqli
  charset: utf8

vo_mirror_database:
  host: localhost
  user:
  password:
  database:
  lazy: TRUE
  driver: mysqli
  charset: utf8

# CHFiling
chfiling:
  packageReference:
  senderId:
  password:
  emailAddress:
  gatewayTest:
  doc_path: "%root%/project/temp/upload/ch_documents"

# feefo
feefo:
  url: "https://ww2.feefo.com/api/entersaleremotely"
  param:
    website: www.madesimplegroup.com/CompaniesMadeSimple
    password:
  merchant_id: company-formation-made-simple
  ignored_curl_errors:
    - 28 # operation timed out
    - 60 # SSL certificate problem
    - 52 # SSL read error
    - 56 # SSL read error

# notifier
notifier:
  url:
  defaultParameters:
    apiKey:
  defaultNamespace: cms.
  oauth:
    token: pass
    secret: pass

# tax assist lead
taxassist:
  driver: mysqli
  host:
  username:
  password:
  database: taxassist_supportsite
  table: lead_enquiries
  lazy: true

debug:
  log:
    dir: "%root%/logs/php"
    error_name: php_error.log
    info_name: php_info.log
  email:
    severity: 500 # numerical representative of Monolog\Logger::CRITICAL
    to: <EMAIL>
    from: CMS <<EMAIL>>
    subject: Error ocurred on www.companiesmadesimple.com
  error_page_dir: "%root%/project/CommonModule/Templates/errorPages"
  stackdriver:
    key:

di:
  paths:
    - "%root%/project/config/di"
    - "%root%/project/ServiceActivatorModule/Config"
    - "%root%/project/LoggableModule/Config"
    - "%root%/project/ServiceSettingsModule/Config"
    - "%root%/vendor/made_simple/msg-framework/utils/src/GoogleServicesModule/Config"
    - "%root%/project/ToolkitOfferModule/Config"
    - "%root%/project/VoServiceModule/Config"
    - "%root%/project/InternationalModule/Config"
    - "%root%/project/OrderModule/Config"
    - "%root%/project/SitemapModule/Config"
    - "%root%/project/FraudProtectionModule/Config"
    - "%root%/project/TemplatingModule/Config"
    - "%root%/project/VoucherModule/Config"
    - "%root%/project/MailgunModule/Config"
    - "%root%/project/InfusionsoftModule/Config"
    - "%root%/project/PayByPhoneModule/Config"
    - "%root%/project/BankHolidayModule/Config"
#    - "%root%/project/EmailModule/Config"
    - "%root%/project/FormSubmissionModule/Config"
    - "%root%/project/CompanyUpdateModule/Config"
    - "%root%/project/AdminModule/Config"
    - "%root%/project/CsmsModule/Config"
    - "%root%/project/FeefoModule/Config"
    - "%root%/project/ValidationModule/Config"
    - "%root%/project/ServicesAdminModule/Config"
    - "%root%/project/SpecialOffersModule/Config"
    - "%root%/project/ProductModule/Config"
    - "%root%/project/WholesaleModule/Config"
    - "%root%/project/CompanyImportModule/Config"

  files:
    - common.xml
    - cron.xml
    - emailers.xml
    - facades.xml
    - factories.xml
    - listeners.xml
    - loggers.xml
    - mappers.xml
    - models.xml
    - repositories.xml
    - services.xml
    - commands.xml
    - controllers.xml
    - content.xml
    - utils.xml
    - di_service_activator.xml
    - di_service_settings.xml
    - di_google_services.xml
    - di_toolkit_offer.xml
    - di_vo_service.xml
    - di_order.xml
    - di_sitemap.xml
    - di_fraud_protection.xml
    - di_international.xml
    - di.xml
    - di_voucher.xml
    - di_mailgun.xml
    - di_infusionsoft.xml
    - di_bank_holiday.xml
#    - di_email.xml
    - di_form_submission.xml
    - di_company_update.xml
    - di_admin.xml
    - di_validation.xml
    - di_services_admin.xml
    - di_special_offers.xml
    - di_company_import.xml
    - "%root%/project/WholesaleModule/Config/di.xml"
    - "%root%/project/LandingPagesModule/Config/di.xml"

doctrine:
  entity_paths:
    - "%root%/project/models/Entities"
    - "%root%/project/LoggableModule/Entities"
  entity_namespace: CMS\Proxy
  proxy_dir: "%root%/storage/proxies"
  dbal:
    types:
      Entities\CompanyHouse\Helper\Authentication:
        class: CompanyFormationModule\DoctrineTypes\AuthenticationType
        db_type: Entities\CompanyHouse\Helper\Authentication
  cache:
    type: apc
    # context can be directory or namespace
    context: cms_doctrine
  enable:
    filters:
      - deletedCustomers
      - deletedCompanies
      - deletedBusinessServicesLeads
      - deletedCsmsReports
      - hiddenCompanies
      - zeroAmountCashBacks
      - deletedOrderData

  orm:
    entity_managers:
      default:
        filters:
          deletedCustomers: CustomerModule\Filters\DeletedCustomersFilter
          deletedCompanies: CompanyModule\Filters\DeletedCompaniesFilter
          deletedBusinessServicesLeads: BusinessServicesModule\Filters\DeletedLeadFilter
          deletedCsmsReports: CsmsModule\Filters\DeletedCsmsReportsFilter
          hiddenCompanies: CompanyModule\Filters\HiddenCompaniesFilter
          zeroAmountCashBacks: CashBackModule\Queries\ZeroAmountCashBackFilter
          deletedOrderData: PaymentModule\Filters\DeletedOrderDataFilter

ui:
  yaml_config_dir: "%root%"
  mustache:
    template_dir: "%root%/www/components/ui_server/templates"
    cache_dir: "%temp_dir%/mustache"
  cache:
    type: filesystem
    context: "%cache_dir%/ui"
  url: /components/ui_server

symfony_forms:
  templates_dirs:
    - "%root%/vendor/symfony/twig-bridge/Resources/views/Form"
    - "%root%/project/FormModule/Templates"
    - "%root%/vendor/made_simple/msg-framework/form_module/src/FormModule/Templates"
  csrf_secret: "asrtastdiestondo34i34g3gifodenf34e56nidhentidsendt34"
  default_themes:
    - form_div_layout.html.twig
  validation_files:
    - "%root%/project/ServicesAdminModule/Config/validations.yml"


twig.cache_dir: "%cache_dir%/twig"

host: www.companiesmadesimple.com
stats_collector: cms

blog_feed:
  request:
    options:
      timeout: 2
      connect_timeout: 2
  fetcher:
    url: https://www.companiesmadesimple.com/blog/xml-feed/
    dir: "%root%/www/webtemp"
    filename: cms_blog.xml
  reader:
    filepath: http://%host%/webtemp/cms_blog.xml

vo:
  base_url: https://www.londonpresence.com/

# api server
vo_api:
  base_url: "https://www.londonpresence.com/api/"
  basicAuth:
    username:
    password:

debug_panel:
  enabled: true
  show_queries: false
  show_admin: true
  show_generator: false
  show_features: true
  show_review: false
  show_cache: false

router:
  cache_dir: "%cache_dir%/routes"
  template_ext: tpl
  template_path: "%root%/project"
  template_folder_name: Templates

routes:
  - "%root%/project/config/routes/routes.yml"

cache:
  enabled: true
  memory:
    type: apc
    context: cms
  filesystem:
    type: filesystem
    context: "%cache_dir%/default"
  redis:
    type: redis
    context: cms
    key: redis_for_cache

notifications:
  cache:
    type: redis
    context: 'notifications'

application:
  cache:
    type: nette.filesystem
    context: "%cache_dir%/nette/cache"

cron:
  cache:
    type: filesystem
    context: "%cache_dir%/cron"

smarty:
  force_compile: false
  cache_dir: "%temp_dir%/templates/cache"
  compiled:
    admin:
      cache_dir: "%temp_dir%/templates/admin"
    front:
      cache_dir: "%temp_dir%/templates/front"
  plugins:
    timestamp:
      type: modifier
      callback:
        class: Utils\Helpers\DateHelper
        method: formatTimestamp

session:
  save_handler: files
  save_path: "%root%/temp/sessions"

storage:
  local:
    default: "%root%/temp/storage"

id3global:
  username: <EMAIL>
  password: password
  wsdl: https://www.id3global.com/ID3gWS/Id3Global.svc?wsdl
  profile:
    UK_ADDRESS_UK_DRIVING_LICENSE:
      id: 0438fdcf-b6cb-4211-bb3d-8b90313bdf16
      version: 1.2
    UK_ADDRESS_EU_ID_CARD:
      id: 8117403b-3c5a-454e-b2ff-65095edfc000
      version: 1.1
    UK_ADDRESS_UK_PASSPORT:
      id: 53cd48a2-964b-4e33-ba2b-5b6bcfa148af
      version: 1.1
    UK_ADDRESS:
      id: 39c50616-55dc-44a8-b6ed-448eb15652bc
      version: 1.1
    INTERNATIONAL_PASSPORT:
      id: 20c73e2c-ee8e-485d-8303-6a07d2cf0c45
      version: 1.1
    UK_COMBINED:
      id: e81ff159-ee27-415d-99d4-ed09d12f3eb0
      version: 1.4
google_services:
  service_account_name:
  service_scopes:
    - https://www.googleapis.com/auth/drive
  key_file_path: "%root%/storage/keys/google_services_key.p12"
  service_name: msg_google_service

toolkit_offers:
  temp_dir: "%root%/temp"
  namesco:
    google_drive_directory_id: ~
    enabled_by_default_starting_date: '2020-04-09'
  adwords:
    google_drive_directory_id: ~

journey_page_offers_order:
  offers:
#    - taxAssist
    - takepayments
#    - worldpay
#    - iwoca
#    - pearl
#    - moneypenny
    - mazuma
    - wisebusiness
  loans:
    - virginStartUp

companies_house:
  character_blacklist: "/([^\x20-\x7F£]|[`|^])/u"
  doc_path: "%root%/project/temp/upload/ch_documents"
  api:
    keys: []

fraud_protection_csv_ftp:
  host: ~
  directory: ~
  username: ~
  password: ~
  ssl: false
  passive: false

#lpl_database:
#    host: ***************
#    username: ~
#    password: ~
#    database: MsgData
#    driver: mssql
#    driverClass: Lsw\DoctrinePdoDblib\Doctrine\DBAL\Driver\PDODblib\Driver
#    lazy: true

template_variables:
  root_path: "%root%/storage/template_variables"
  cache_life_time_in_seconds: 3600
  cache:
    type: apc
    context: tmpl_variables
    key:

bank_holidays:
  url: https://www.gov.uk/bank-holidays.json
  cache_lifetime: 10080

infusionsoft:
  base_url: bo281.infusionsoft.com
  api_key:
  port: 443
  username:
  passwords:
    - dummypass1
    - password2
  placeholders:
    owner:
      first_name: Made Simple
      last_name: Team
      email: <EMAIL>

csms:
  api_key: ~
  apiKey: "@.@.@.M4d3S1mP73Gr0Up.@.@.@"  # -> For the CMS Integration Module
  url: https://www.companysearchesmadesimple.com/api/
  serverIp: ************

admin_add_service:
  available_products:
    - '1609' # Confirmation statement Express
    - '340'  # Confirmation statement service
    - '666'  # Confirmation statement (DIY)
    - '1740' # Confirmation statement & Dormant company accounts bundle
    - '1717' # Psc register & Confirmation statement
    - '1706' # Psc register & Confirmation statement DIY
    - '1705' # Psc register
    - '1353' # Privacy package
#    - '342'  # Comprehensive/Ultimate package
    - '401' # RACN
    - 'package_comprehensive_renewal'  # Comprehensive renewal
    - 'package_ultimate_renewal'  # Ultimate renewal
    - '1493' # International package
#    - '806'  # Service address
    - '334'  # Registered office
    - 'company_secretarial_service_product' # Company Secretarial Service
    - 'company_protection_product' # Fraud Protection (Company Protection)
    - 'product_cancellation_protection' # Cancellation protection - 1 year
    - 'international_package_renewal_monthly' # International Package Renewal (Monthly)
    - 'privacy_package_renewal_monthly_08_2023' # Privacy Package Renewal (Monthly)
    - 'comprehensive_package_renewal_monthly_08_2023' # Comprehensive Package Renewal (Monthly)
    - 'ultimate_package_renewal_monthly_08_2023' # Ultimate Package Renewal (Monthly)
    - 'full_privacy_package_monthly_08_2023' # Full Privacy Package (Monthly)
    - 'mailbox_standard_renewal_monthly' # Mailbox Standard Renewal (Monthly)
    - 'mailbox_premium_renewal_monthly' # Mailbox Premium Renewal (Monthly)
    - 'mailbox_business_address_renewal_monthly' # Mailbox Business Address Renewal (Monthly)

barclays:
  token_url:
  test_token_url:
  submission_path: /services/apexrest/BBALead
  consumer_key:
  consumer_secret:
  username:
  password:
  gateway_test:
  test:
    token_url: https://test.salesforce.com/services/oauth2/token
    submission_path: /services/apexrest/BBALead
    consumer_key:
    consumer_secret:
    username:
    password:

use_new_config_loading: true

worldpay_lead_loading_api:
  username:
  password:
  host:
  ta_code:

takepayments:
  email_address:

redis:
  host: localhost
  port: 6379
  timeout: 2
  password:

basket:
  non_package_products:
    products_and_offers:
      master_list_node: 1725

tide:
  auth:
    api_id: ****************
    api_key: 2a4gKL2Gs9&1-GlN
  api:
    base_url: https://api-staging.tide.co

efiling:
  api:
    api_key: ""
    base_url:
    version:

sage_business_services:
  base_url: https://s**********.t.eloqua.com/e/f2
  form:
    elqSiteID: **********
    elqFormName: NPS_17Q1_OT_GL_DGWW_NPS_ExternalFormHighScore
    elqCookieWrite: 0
    campaignID: 7011o000000q2At
    product: Sage Business Cloud Accounting
    content: CL_ACG_GB_DEM_madesimpletrial_EXT|MID|GNRC
    utm_campaign: ACG_18Q2_NCM_GBIE_DGCS_AC0840_MadeSimpleCampaign1
    utm_source: Website
    utm_medium: Partners-Websites
    optedId: 1
    status: Responded
  email:
    start_trial_link: https://app.sageone.com/signup?product=accounting&opc=e3a8768cc53f975903d0db5ad4abd99a27072afb
    start_trial_with_sage_link: https://app.sageone.com/signup?product=accounting&opc=e3a8768cc53f975903d0db5ad4abd99a27072afb

google_optimize:
  code: GTM-54HBLK5

google_tag_manager:
  code: GTM-5RVST5

google:
  datastore:
    id:
      keyFilePath: "%root%/storage/keys/id-documents-result-codes.json"
    emails:
      keyFilePath: "%root%/storage/keys/datastore-emails.json"
    experiments:
      keyFilePath: "%root%/storage/keys/datastore-emails.json"

get_address:
  api:
    key: gMhlRwQNUEGprdiuiFUBpg24252
    base_url: https://api.getAddress.io/find

mettle:
  api:
    url:
    token:

jumio:
  id: empty
  secret: empty

mantle:
  api:
    url:
    token:

mailroom:
  api:
    url: 'https://mailroom.msg.cool/api/'
    token: ""

mailscan:
  mail_recipients:
    forwarding_request: '<EMAIL>'

order_data:
  log_file_path: "%root%/logs/order_data_retrieval.log"
  exceptions_dir_path: "%root%/logs/order_data_exceptions"

big_query_credentials:
  projectId:
  environment:
  keyFilePath:
  retries:

firebase:
  auth:
    project_id:
    api_key:
    emulator_url:
    url: 'https://identitytoolkit.googleapis.com/v1/accounts:'
  admin:
    url: 'https://sc.msg.cool/'

xeinadin:
  leadEmailRecipient:

omnipay:
  username: api-key
  password:
  token_password:
  url: https://omnipay.companiesmadesimple.com/
  local_component_url:
  stripe_public_key: pk_live_51JxEzjJXCdOXyGnoaPVZPMLqzbfcJDUw5FAAMoWGkmAq97wby1cDmRycF1dDjx2J5yzk4IgVX53lqvuxFzweRwCc007oQMrHgj
  stripe_direct_debit_enabled: false
  cms_domain: https://companiesmadesimple.com

posted_mail:
  username: mantle-reloaded
  password: 113b71f5bfd2577471a5043be520efad
  url: https://europe-west1-madesimplegroup-151616.cloudfunctions.net/cms-posted-mail/

env:
  environment: production

ghost:
  url: https://cms.companiesmadesimple.com/ghost/api/
  key: 622d13a6d448f2f8d207885949

sentry:
  enabled: true
  dsn: 'https://<EMAIL>/3'

strapi:
  url: https://cdn.companiesmadesimple.com/api/
  key: notfilledin
  cache:
    business_services:
      type: apcu
      context: 'strapi_bs'

sendinblue:
  url: https://api.sendinblue.com/v3/smtp/
  auth:
    api_key: 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

credas:
  url: https://portal.credas.com/api/
  api_key: 'NWM3ZDBlZDktYjQ3YS00NjE0LThkMWYtMmYzYTE1YTUwZmI0'
  wholesale_api_key: 'Y2I4ZjIzMjAtODk2Ny00OWQ2LTk2OTAtMTc3MzM0MmZmZGYw'
  user_group_id: '788b9c6a-b436-4acc-9f59-5b476a08e98f'
  wholesale_user_group_id: 'b40ea854-fe47-408f-9137-417496260bbb'
  journeys:
    CREDAS_HIGH_RISK:
      journeyId: '8e8196c4-6e5d-4e38-91e9-382979e6bcc5'
      actorId: 279
    CREDAS_BASIC_NON_UK:
      journeyId: '196392eb-1206-4fc9-aa28-3a3a1f8887b9'
      actorId: 281
    CREDAS_BASIC_UK:
      journeyId: 'a7344105-e4b5-4a09-8a14-045052d54df5'
      actorId: 280
    CREDAS_CORPORATE_ENTITY:
      journeyId: 'c47b6749-2bd4-4606-be64-476bbaa33486'
      actorId: 698
  wholesaleJourneys:
    CREDAS_HIGH_RISK:
      journeyId: 'fa8a7f26-0dbb-41ac-bbf6-317edc3c9098'
      actorId: 312
    CREDAS_BASIC_NON_UK:
      journeyId: 'bbb0d9b6-f249-443c-bfbe-9e955f0ba3ce'
      actorId: 314
    CREDAS_BASIC_UK:
      journeyId: '2d576d92-9dfa-49dd-b136-a474b2e948d2'
      actorId: 313
    CREDAS_CORPORATE_ENTITY:
      journeyId: 'c47b6749-2bd4-4606-be64-476bbaa33486'
      actorId: 698

google_analytics:
  api:
    secret:
    measurement_id:
    url: 'https://www.google-analytics.com/mp/collect?'

pubsub:
  enabled: false
  projectId: 'companiesmadesimple-208508'
  secret: ''
  keyFilePath: '%root%/storage/keys/pubsub.json'
  topics:
    webhook: 'projects/companiesmadesimple-208508/topics/cms-webhook-events'
    pull: 'projects/companiesmadesimple-208508/topics/cms-pull-events'
    deadLetter: 'projects/companiesmadesimple-208508/topics/cms-failed-events'
  subscriptions:
    webhook: 'projects/companiesmadesimple-208508/subscriptions/cms-webhook-events'
    pull: 'projects/companiesmadesimple-208508/subscriptions/cms-pull-events'
    deadLetter: 'projects/companiesmadesimple-208508/subscriptions/cms-failed-events'
  failedMessages:
    table: 'pubsub.cms-failed-events'

cashplus:
  productName: CashplusBusDeluxe

templating:
  base_url:
    site: 'https://site.companiesmadesimple.com'
    google_apis: 'https://storage.googleapis.com/'