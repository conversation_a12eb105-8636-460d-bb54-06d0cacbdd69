<?php

declare(strict_types=1);

namespace ServicesAdminModule\Facades;

use Entities\Company;
use Exceptions\Technical\NodeException;
use Libs\Exceptions\EntityNotFound;
use ProductModule\Repositories\ProductRepository;
use ServiceActivatorModule\ProductRelationshipChecker;
use ServiceActivatorModule\RelatedServiceProvider;
use Services\ProductService;
use ServicesAdminModule\Entities\AddServiceFormOrderFactory;
use ServicesAdminModule\Forms\AddServiceFormData;
use ServiceSettingsModule\Services\ServiceSettingsService;

class AddServiceFacade
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly ProductService $productService,
        private readonly AddServiceFormOrderFactory $orderFactory,
        private readonly RelatedServiceProvider $relatedServiceProvider,
        private readonly ProductRelationshipChecker $productChecker,
        private readonly ServiceSettingsService $serviceSettingsService,
    ) {
    }

    /**
     * @throws NodeException
     * @throws EntityNotFound
     */
    public function addService(AddServiceFormData $data, Company $company): void
    {
        $product = $this->productRepository->getProductByName($data->getProductName());
        $related = $this->relatedServiceProvider->getRelatedService($company, $product, new \DateTime());
        $renewalToken = null;
        if ($related && $this->productChecker->isEqual($related->getProduct(), $product)) {
            $renewalSettings = $this->serviceSettingsService->getSettingsByService($related);
            if ($renewalSettings) {
                $renewalToken = $renewalSettings->getRenewalToken();
            }
        }
        $orderItem = $this->orderFactory->create($company, $product, $data);

        $product->setCompanyId($company->getId());
        $product->setOrderItem($orderItem);
        $product->setDuration($data->getDuration());
        $product->setActivateFrom($data->getStartDateDateTime());

        $this->productService->saveServices([$product], $renewalToken);
    }
}
