<?php

declare(strict_types=1);

namespace ServicesAdminModule\Entities;

use Entities\Company;
use Entities\Customer;
use Entities\Order;
use Entities\OrderItem;

use function FunctionalModule\Helpers\Object\mapToArray;
use function FunctionalModule\Helpers\Object\sum;

use Models\Products\BasketProduct;
use Services\UserService;
use ServicesAdminModule\Forms\AddServiceFormData;
use UserModule\Contracts\IUser;
use VatModule\Calculators\VatCalculator;

use function Widmogrod\Functional\invoke;

class AddServiceFormOrderFactory
{
    /**
     * @var UserService
     */
    private $userService;

    /**
     * @var VatCalculator
     */
    private $vatCalculator;

    /**
     * @param UserService   $userService
     * @param VatCalculator $vatCalculator
     */
    public function __construct(UserService $userService, VatCalculator $vatCalculator)
    {
        $this->userService = $userService;
        $this->vatCalculator = $vatCalculator;
    }

    /**
     * @param Company            $company
     * @param BasketProduct      $product
     * @param AddServiceFormData $data
     * @param IUser|null         $user
     *
     * @return OrderItem
     */
    public function create(Company $company, BasketProduct $product, AddServiceFormData $data, ?IUser $user = null)
    {
        $order = $this->createOrder($company->getCustomer(), $data, $user);
        $orderItem = $this->createOrderItem($data, $order, $company, $product);
        $order->addItem($orderItem);
        $this->calculateOrder($order, [$orderItem], $data);

        return $orderItem;
    }

    /**
     * @return OrderItem[]
     */
    public function createFromTransfer(Company $company, array $products, IUser $actionBy): array
    {
        $data = new AddServiceFormData();
        $order = $this->createOrder($company->getCustomer(), $data, $actionBy);
        $order->setDescription('Company transfer');
        $order->setPaymentMediumId(Order::PAYMENT_MEDIUM_TRANSFERRED);
        $orderItems = mapToArray(function (BasketProduct $product) use ($data, $order, $company) {
            $orderItem = $this->createOrderItem($data, $order, $company, $product);
            $order->addItem($orderItem);

            return $orderItem;
        }, $products);
        $this->calculateOrder($order, $orderItems, $data);

        return $orderItems;
    }

    private function calculateOrder(Order $order, array $orderItems, AddServiceFormData $data)
    {
        if ($data->isReasonGoodwill()) {
            $subtotal = sum(invoke('getSubTotal'), $orderItems);
            $order->setRealSubtotal($subtotal);
            $order->setSubTotal(0);
            $order->setDiscount($subtotal);
            $order->setVat(0);
            $order->setTotal(0);
        } else {
            $total = $data->getTotalAmount();
            $subtotal = $this->vatCalculator->getSubtotalFromTotal($total);

            $order->setRealSubtotal($subtotal);
            $order->setSubTotal($subtotal);
            $order->setDiscount(0);
            $order->setVat($this->vatCalculator->getVatFromTotal($total));
            $order->setTotal($total);
        }
    }

    /**
     * @param Customer           $customer
     * @param AddServiceFormData $data
     * @param IUser|null         $user
     *
     * @return Order
     */
    private function createOrder(Customer $customer, AddServiceFormData $data, ?IUser $user = null)
    {
        $order = new Order($customer);

        if (!$data->isReasonCommand()) {
            $order->setAgent($user ? $this->userService->getUserById($user->getId()) : $this->userService->getLoggedInUser());
        }

        if ($data->isReasonGoodwill()) {
            $order->setDescription("Goodwill Gesture ({$data->getDuration()})");
            $order->setPaymentMediumId(Order::PAYMENT_MEDIUM_COMPLIMENTARY);
        } else {
            $order->setDescription("Non-standard payment, payment date: {$data->getPaymentDate()->format('d/m/Y')}, reference: {$data->getReference()}");
            $order->setPaymentMediumId(Order::PAYMENT_MEDIUM_NON_STANDARD);
        }

        return $order;
    }

    /**
     * @param AddServiceFormData $data
     * @param Order              $order
     * @param Company            $company
     * @param BasketProduct      $product
     *
     * @return OrderItem
     */
    private function createOrderItem(AddServiceFormData $data, Order $order, Company $company, BasketProduct $product)
    {
        $orderItem = new OrderItem($order);
        $orderItem->setCompany($company);
        $orderItem->setProductId($product->getId());
        $orderItem->setProductTitle("{$product->getLongTitle()} for company \"{$company->getCompanyName()}\"");
        $orderItem->setPrice($product->getPrice());
        $orderItem->setSubTotal($product->getPrice());
        $orderItem->setVat($product->getVat());
        $orderItem->setTotalPrice($product->getTotalVatPrice());
        $orderItem->setIsFee(false);
        $orderItem->setNotApplyVat(false);
        $orderItem->setNonVatableValue($product->nonVatableValue ?? 0); /* @phpstan-ignore-line */
        $orderItem->setQty($data->isReasonGoodwill() ? 0 : 1);

        return $orderItem;
    }
}
