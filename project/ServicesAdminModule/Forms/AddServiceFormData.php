<?php

declare(strict_types=1);

namespace ServicesAdminModule\Forms;

class AddServiceFormData
{
    /**
     * @var string
     */
    private $productName;

    /**
     * @var string
     */
    private $startDate;

    /**
     * @var string
     */
    private $duration;

    /**
     * @var string
     */
    private $reason = AddServiceForm::REASON_GOODWILL;

    /**
     * @var string
     */
    private $paymentDate;

    /**
     * @var float
     */
    private $totalAmount;

    /**
     * @var string
     */
    private $reference;

    /**
     * @return string
     */
    public function getProductName()
    {
        return $this->productName;
    }

    /**
     * @param int $productId
     */
    public function setProductName(string|int $productName): self
    {
        $this->productName = $productName;

        return $this;
    }

    /**
     * @return string
     */
    public function getStartDate()
    {
        return $this->startDate;
    }

    /**
     * @param string $startDate
     */
    public function setStartDate($startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    /**
     * @return \DateTime|null
     */
    public function getStartDateDateTime()
    {
        return $this->startDate ? new \DateTime($this->startDate) : null;
    }

    /**
     * @return string
     */
    public function getDuration()
    {
        return $this->duration;
    }

    /**
     * @param string $duration
     */
    public function setDuration($duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * @return string
     */
    public function getReason()
    {
        return $this->reason;
    }

    /**
     * @param string $reason
     */
    public function setReason($reason): self
    {
        $this->reason = $reason;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getPaymentDate()
    {
        return $this->paymentDate;
    }

    /**
     * @param \DateTime $paymentDate
     */
    public function setPaymentDate(?\DateTime $paymentDate = null): self
    {
        $this->paymentDate = $paymentDate;

        return $this;
    }

    /**
     * @return float
     */
    public function getTotalAmount()
    {
        return $this->totalAmount;
    }

    /**
     * @param float $totalAmount
     */
    public function setTotalAmount($totalAmount): self
    {
        $this->totalAmount = $totalAmount;

        return $this;
    }

    /**
     * @return string
     */
    public function getReference()
    {
        return $this->reference;
    }

    /**
     * @param string $reference
     */
    public function setReference($reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    /**
     * @return bool
     */
    public function isReasonGoodwill()
    {
        return $this->reason == AddServiceForm::REASON_GOODWILL;
    }

    /**
     * @return bool
     */
    public function isReasonNonStandard()
    {
        return $this->reason == AddServiceForm::REASON_NON_STANDARD;
    }

    public function isReasonCommand(): bool
    {
        return $this->reason === AddServiceForm::REASON_COMMAND;
    }
}
