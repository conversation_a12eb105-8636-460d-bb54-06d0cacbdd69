<?php

declare(strict_types=1);

namespace MailScanModule\Factories;

use CompanyModule\Facades\PostItemHandlingFacade;
use Entities\Company;
use Entities\Service;
use Framework\FNode;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Deciders\ForwardingAddressDecider;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Deciders\ReleaseItemDecider;
use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Services\PostItemService;
use Psr\Log\LoggerInterface;

class MailboxEmailFactory
{

    public function __construct(
        private MailboxTierDecider $mailboxTierDecider,
        private PostItemHandlingFacade $postItemHandlingFacade,
        private MailForwardingAddressFacade $mailForwardingAddressFacade,
        private PostItemService $postItemService,
        private readonly ReleaseItemDecider $releaseItemDecider,
        private readonly ForwardingAddressDecider $forwardingAddressDecider,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function createFromMailroomPostItemData(Company $company, MailroomPostItemData $item): MailboxEmailData
    {
        $isReleased = $item->isDownloadableStatus();
        $hasFailedToCharge = ($item->hasUnpaidQuotaCharges() && !$isReleased);
        $lastEmailSent = $item->getDetail(MailroomApiClient::LAST_EMAIL_SENT_DETAIL_NAME);
        $hasIdCheck = $this->releaseItemDecider->isIdCheckCompleted($company);
        $needsForwardingAddress = $this->forwardingAddressDecider->companyNeedsMailForwardingAddressByFormat($company, $item->getFormat());

        $mailboxService = $company->getActiveOrLatestMailboxService();
        if (!is_null($mailboxService) && $mailboxService->isActive()) {
            $mailboxTier = $this->mailboxTierDecider->determineMailboxTier($mailboxService);
            $packageName = $this->getPackageName($company, $mailboxService);
            $mailboxInitialProduct = $this->postItemService->getProductByTier($mailboxTier, $company);
            $handlingSettingValue = $this->postItemHandlingFacade->getHandlingSettingByType(
                $company,
                $item,
                $mailboxInitialProduct,
                $mailboxTier
            );
            $forwardingAddress = $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->getFullAddress();
            $isOverdue = $mailboxService->isOverdueAndExpired();
            $maximumQuota = max(
                MailboxProductPropertyHelper::getMaximumQuotaByTypeAndProcessingMethod(
                    $mailboxInitialProduct,
                    $item->getType(),
                    $handlingSettingValue
                ),
                MailboxProductPropertyHelper::getMaximumGenericQuotaByTypeAndProcessingMethod(
                    $mailboxInitialProduct,
                    MailboxProductPropertyHelper::getProcessingMethodAsString($item->getType(), $handlingSettingValue),
                )
            );
            $quotaPrice = MailboxProductPropertyHelper::getExtraQuotaFeeByFormat(
                $mailboxInitialProduct,
                $item->getFormat()
            );
            $releasePrice = MailboxProductPropertyHelper::getPayToReleaseFeeByFormat(
                $mailboxInitialProduct,
                $item->getFormat()
            );
            $withinQuota = $handlingSettingValue !== 0 ? (bool) $this->postItemService->isWithinQuotaMaximum(
                $company,
                $mailboxInitialProduct,
                $item,
                $handlingSettingValue
            ) : null;
        }

        return new MailboxEmailData(
            $company->getCompanyName(),
            $company->getId(),
            $company->getCustomer()->getFirstName() ?? '', // @phpstan-ignore-line
            $company->getCustomer()->getFullName() ?? '', // @phpstan-ignore-line
            $item->getType(),
            $isReleased,
            $item->getStatus(),
            $item->getDtc(),
            $this->getParsedMailSenderHtml($item),
            $hasFailedToCharge,
            $withinQuota ?? null,
            $lastEmailSent,
            $handlingSettingValue ?? null,
            $mailboxTier ?? null,
            $packageName ?? null,
            $forwardingAddress ?? null,
            $maximumQuota ?? null,
            isset($quotaPrice) ? $this->getPriceLabelFromFloat($quotaPrice) : null,
            isset($releasePrice) ? $this->getPriceLabelFromFloat($releasePrice) : null,
            $isOverdue ?? null,
            $hasIdCheck,
            $needsForwardingAddress
        );
    }

    private function getPriceLabelFromFloat(float $price): string
    {
        return sprintf(
            '£%s',
            number_format($price, 2)
        );
    }

    private function getParsedMailSenderHtml(MailroomPostItemData $postItem): string
    {
        if ($postItem->getType() === PostItemTypeEnum::TYPE_STATUTORY->value) {
            return sprintf(
                '<a href="https://support.companiesmadesimple.com/hc/en-us/articles/360002313778-What-is-Government-Mail">%s</a> mail',
                $postItem->getSender()
            );
        }

        if ($postItem->getType() === PostItemTypeEnum::TYPE_PARCEL->value) {
            return sprintf(
                '<b>%s</b>',
                $postItem->getSender()
            );
        }

        return sprintf(
            '<a href="https://support.companiesmadesimple.com/hc/en-us/articles/35038298824849-What-is-Business-Mail">%s</a> mail',
            $postItem->getSender()
        );
    }

    private function getPackageName(Company $company, Service|FNode $mailboxService): string
    {
        try {
            $corePackageService = $company->getActiveCorePackageService();

            if (is_null($corePackageService) || $mailboxService->isMailboxBusinessAddressService()) {
                return $this->mailboxTierDecider->getTierName($mailboxService);
            }

            return $corePackageService->getServiceName();
        } catch (\Throwable $e) {
            $this->logger->error(sprintf(
                'Error getting package name for company %d: %s',
                $company->getId(),
                $e->getMessage()
            ));

            return 'Package';
        }
    }
}
