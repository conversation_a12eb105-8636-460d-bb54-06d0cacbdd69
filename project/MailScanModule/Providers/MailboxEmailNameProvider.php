<?php

declare(strict_types=1);

namespace MailScanModule\Providers;

use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use MailScanModule\Deciders\MailboxEmailDecider;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Emailers\MailboxEmailer;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;

readonly class MailboxEmailNameProvider
{
    public const MAIL_NO_SERVICE = 'mail-no-service';
    public const MAIL_RELEASED_COLLECT = 'mail-released-collect';
    public const MAIL_RELEASED_POST = 'mail-released-post';
    public const MAIL_RELEASED_SCAN = 'mail-released-scan';
    public const MAIL_SERVICE_OVERDUE_AND_NO_CHECK_ID = 'mail-service-overdue-and-no-check-id';
    public const MAIL_SERVICE_OVERDUE = 'mail-service-overdue';
    public const NO_MAIL_ID_CHECK = 'mail-no-id-check';
    public const NO_PARCEL_ID_CHECK = 'parcel-no-id-check';
    public const MAIL_NO_SERVICE_AND_NO_ID_CHECK = 'mail-no-service-and-no-id-check';
    public const PARCEL_NO_SERVICE_AND_NO_ID_CHECK = 'parcel-no-service-and-no-id-check';
    public const PARCEL_NO_SERVICE = 'parcel-no-service';
    public const PARCEL_RELEASED_COLLECT = 'parcel-released-collect';
    public const PARCEL_RELEASED_COLLECT_REMINDER = 'parcel-released-collect-reminder';
    public const PARCEL_SERVICE_OVERDUE = 'parcel-service-overdue';
    public const PARCEL_SERVICE_OVERDUE_AND_NO_CHECK_ID = 'parcel-service-overdue-and-no-check-id';
    public const PARCEL_WAITING_PAYMENT_POST = 'parcel-waiting-payment-post';
    public const PARCEL_WAITING_PAYMENT_POST_REMINDER = 'parcel-waiting-payment-post-reminder';
    public const PARCEL_RTS = 'parcel-rts';
    public const UNPAID_CHARGE = 'unpaid-charge';
    public const MAIL_WAITING_PAYMENT_SCAN = 'mail-waiting-payment-scan';
    public const MISSING_FORWARDING_ADDRESS = 'missing-forwarding-address';
    public const REMINDER_EMAILS = [
        self::PARCEL_RELEASED_COLLECT => self::PARCEL_RELEASED_COLLECT_REMINDER,
        self::PARCEL_WAITING_PAYMENT_POST => self::PARCEL_WAITING_PAYMENT_POST_REMINDER,
    ];

    public function __construct(
        private MailboxEmailDecider $mailboxEmailDecider,
    ) {
    }

    public function getMailboxEmailName(MailboxEmailData $emailData, ?string $processingStep): ?string
    {
        return match (true) {
            $emailData->needsForwardingAddress => self::MISSING_FORWARDING_ADDRESS,
            $this->mailboxEmailDecider->isAReminderEmail($emailData->lastEmailSent) => $emailData->lastEmailSent,
            $emailData->isServiceOverdue => $this->getOverdueMailboxServiceEmailName(PostItemTypeEnum::from($emailData->postItemType), $emailData->hasIdCheck),
            is_null($emailData->mailboxTier) => $this->getNoServiceEmailName(PostItemTypeEnum::from($emailData->postItemType), $emailData->hasIdCheck),
            !$emailData->hasIdCheck => $this->getNoIdCheckEmailName($emailData->postItemType),
            $emailData->hasFailedToCharge => self::UNPAID_CHARGE,
            $this->isEmailEligibleParcel($emailData) => $this->getParcelEmailName($emailData),
            $emailData->isReleased => $this->getReleasedMailEmailName($emailData),
            $emailData->postItemType === PostItemTypeEnum::TYPE_NON_STATUTORY->value => self::MAIL_WAITING_PAYMENT_SCAN,
            $processingStep === MailboxEmailer::PROCESSING_STEP_UPDATE => null,
            default => throw new \RuntimeException(sprintf('Unhandled email template name for emailData: %s', json_encode($emailData))),
        };
    }

    public function getReminderEmailName(string $emailName): ?string
    {
        return self::REMINDER_EMAILS[$emailName] ?? null;
    }

    private function getParcelEmailName(MailboxEmailData $emailData): ?string
    {
        if ($emailData->mailboxTier === MailboxTierDecider::TIER_1 || $emailData->mailboxTier === null) {
            return self::PARCEL_RTS;
        }

        return match ($emailData->handlingSetting) {
            PostItemHandlingSetting::VALUE_PARCEL_COLLECT => self::PARCEL_RELEASED_COLLECT,
            PostItemHandlingSetting::VALUE_PARCEL_POST => self::PARCEL_WAITING_PAYMENT_POST,
            PostItemHandlingSetting::VALUE_PARCEL_REJECT => null, // @todo mailbox phase 3
            default => throw new \RuntimeException(sprintf('Unhandled parcel email template name for this handling setting: %s', $emailData->handlingSetting)),
        };
    }

    private function getOverdueMailboxServiceEmailName(PostItemTypeEnum $postItemType, bool $hasIdCheck): string
    {
        if (!$hasIdCheck) {
            return match ($postItemType) {
                PostItemTypeEnum::TYPE_PARCEL => self::PARCEL_SERVICE_OVERDUE_AND_NO_CHECK_ID,
                PostItemTypeEnum::TYPE_STATUTORY, PostItemTypeEnum::TYPE_NON_STATUTORY => self::MAIL_SERVICE_OVERDUE_AND_NO_CHECK_ID,
                default => throw new \RuntimeException(sprintf('Unhandled overdue and no id check email template name for this post item type: %s', $postItemType->value)),
            };
        }

        return match ($postItemType) {
            PostItemTypeEnum::TYPE_PARCEL => self::PARCEL_SERVICE_OVERDUE,
            PostItemTypeEnum::TYPE_STATUTORY, PostItemTypeEnum::TYPE_NON_STATUTORY => self::MAIL_SERVICE_OVERDUE,
            default => throw new \RuntimeException(sprintf('Unhandled overdue email template name for this post item type: %s', $postItemType->value)),
        };
    }

    private function getNoServiceEmailName(PostItemTypeEnum $postItemType, bool $hasIdCheck): string
    {
        if (!$hasIdCheck) {
            return match ($postItemType) {
                PostItemTypeEnum::TYPE_PARCEL => self::PARCEL_NO_SERVICE_AND_NO_ID_CHECK,
                PostItemTypeEnum::TYPE_STATUTORY, PostItemTypeEnum::TYPE_NON_STATUTORY => self::MAIL_NO_SERVICE_AND_NO_ID_CHECK,
                default => throw new \RuntimeException(sprintf('Unhandled no service and no id check email template name for this post item type: %s', $postItemType->value)),
            };
        }

        return match ($postItemType) {
            PostItemTypeEnum::TYPE_PARCEL => self::PARCEL_NO_SERVICE,
            PostItemTypeEnum::TYPE_STATUTORY, PostItemTypeEnum::TYPE_NON_STATUTORY => self::MAIL_NO_SERVICE,
            default => throw new \RuntimeException(sprintf("Unhandled 'no service' email template name for this post item type: %s", $postItemType->value)),
        };
    }

    private function getReleasedMailEmailName(MailboxEmailData $emailData): string
    {
        return match ($emailData->handlingSetting) {
            PostItemHandlingSetting::VALUE_ITEM_SCAN_ONLY => self::MAIL_RELEASED_SCAN,
            PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_COLLECT => self::MAIL_RELEASED_COLLECT,
            PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_POST => self::MAIL_RELEASED_POST,
            default => throw new \RuntimeException(sprintf("Unhandled 'released' email template name for this post item: %s", json_encode($emailData))),
        };
    }

    private function isEmailEligibleParcel(MailboxEmailData $emailData): bool
    {
        return $emailData->postItemType === PostItemTypeEnum::TYPE_PARCEL->value
            && $emailData->itemStatus !== StatusEnum::STATUS_SECURELY_DESTROYED->value
            && $emailData->itemStatus !== StatusEnum::STATUS_TO_BE_SECURELY_DESTROYED->value;
    }

    private function getNoIdCheckEmailName(string $postItemType): string
    {
        return match ($postItemType) {
            PostItemTypeEnum::TYPE_PARCEL->value => self::NO_PARCEL_ID_CHECK,
            PostItemTypeEnum::TYPE_STATUTORY->value, PostItemTypeEnum::TYPE_NON_STATUTORY->value => self::NO_MAIL_ID_CHECK,
            default => throw new \RuntimeException(sprintf('Unhandled no id check email template name for this post item type: %s', $postItemType)),
        };
    }
}
