<?php

namespace Repositories;

use CompanyModule\Entities\Events\CompanyEvent_Abstract;
use CompanyModule\Entities\Events\EmailEvent;
use DateTime;
use Doctrine\DBAL\Types\Type;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Doctrine\ORM\QueryBuilder;
use Entities\Company;
use Entities\Customer;
use Entities\Order;
use Entities\OrderItem;
use Entities\Payment\Token;
use Entities\Service;
use Entities\ServiceSettings;
use ServiceRemindersModule\Entities\ReminderService;
use Utils\Date;
use Repositories\BaseRepository_Abstract;

class ServiceRepository extends BaseRepository_Abstract
{
    public function getServiceById(int $id): ?Service
    {
        return $this->_em->createQueryBuilder()
            ->select('s')
            ->from($this->_entityName, 's')
            ->innerJoin(
                Company::class,
                'c',
                Join::WITH,
                'c.companyId = s.company'
            )
            ->where('s.serviceId = :id')->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param Company $company
     * @return QueryBuilder
     */
    public function getListBuilder(Company $company = NULL)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('s')->from('Entities\Service', 's')
            ->where('s.parent IS NULL')
            ->orderBy('s.dtc', 'DESC');
        if ($company) {
            $qb->andWhere('s.company = :company')
                ->setParameter('company', $company);
        }
        return $qb;
    }

    /**
     * @param Company $company
     * @param string $serviceTypeId
     * @param Service $service
     * @return Service | null
     */
    public function getLastService(Company $company, $serviceTypeId, Service $service = NULL): ?Service
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('s')->from('Entities\Service', 's')
            ->where('s.company = :company')
            ->setParameter('company', $company)
            ->andWhere('s.serviceTypeId = :serviceTypeId')
            ->setParameter('serviceTypeId', $serviceTypeId)
            ->orderBy('s.dtExpires', 'DESC');
        if ($service) {
            $qb->andWhere('s.serviceId != :serviceId')
                ->setParameter('serviceId', $service->getId());
        }
        $services = $qb->getQuery()->getResult();
        return !empty($services) ? $services[0] : NULL;
    }

    /**
     * @param OrderItem $orderItem
     * @return Service[]
     */
    public function getServiceByOrderItem(OrderItem $orderItem)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('s')->from('Entities\Service', 's')
            ->where('s.orderItem = :orderItem')->setParameter('orderItem', $orderItem)
            ->andWhere('s.parent IS NULL');

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param OrderItem $orderItem
     * @return Service[]
     */
    public function getOrderItemServices(OrderItem $orderItem)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('s')->from('Entities\Service', 's')
            ->where('s.order = :order')
            ->andWhere('s.productId = :productId')
            ->andWhere('s.parent IS NULL')
            ->setParameters(
                [
                    'productId' => $orderItem->getProductId(),
                    'order' => $orderItem->getOrder(),
                ]
            );
        return $qb->getQuery()->getResult();
    }

    /**
     * @param int $productId
     * @return IterableResult
     */
    public function getServicesByProductId($productId)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('s')
            ->from('Entities\Service', 's')
            ->where('s.productId = :productId')
            ->setParameter('productId', $productId);
        return $qb->getQuery()->iterate();
    }

    /**
     * @param Date $from
     * @param Date $to
     * @param string $event
     * @return IterableResult
     * @description This method is similar to getServicesDataExpiringWithinDatesForEvent()
     * but it doesn't take tokens into consideration
     */
    public function getAllServicesDataExpiringWithinDatesForEvent(
        Date $from,
        Date $to,
             $event,
        bool $enabledForMonthlyServices = true
    )
    {
        $rsm = new ResultSetMappingBuilder($this->_em);
        $rsm->addRootEntityFromClassMetadata('Entities\Service', 's');
        $rsm->addJoinedEntityFromClassMetadata(
            'Entities\Company',
            'c',
            's',
            'company',
            [],
            ResultSetMappingBuilder::COLUMN_RENAMING_INCREMENT
        );

        $selectClause = $rsm->generateSelectClause(['s' => 's', 'c' => 'c']);

        if ($enabledForMonthlyServices) {
            $query = "SELECT {$selectClause}
            FROM cms2_services s
            INNER JOIN (
                SELECT MAX(dtExpires) AS maxDtExpires, companyId, serviceTypeId
                FROM cms2_services
                WHERE
                    parentId IS NULL
                    AND stateId = :enabledService
                    AND dtStart IS NOT NULL AND dtExpires IS NOT NULL
                GROUP BY companyId, serviceTypeId
            ) AS latestService
            ON (
              s.dtExpires = latestService.maxDtExpires
              AND s.companyId = latestService.companyId
              AND s.serviceTypeId = latestService.serviceTypeId
            )
            INNER JOIN ch_company c ON c.company_id = s.companyId AND c.deleted = 0
            INNER JOIN cms2_customers cu ON cu.customerId = c.customer_id AND cu.deleted = 0
            INNER JOIN cms2_service_settings ss ON (ss.companyId = c.company_id AND ss.serviceTypeId = s.serviceTypeId)
            LEFT JOIN cms2_events e ON (e.objectId = s.serviceId AND eventKey = :eventKey)
            WHERE
                s.parentId IS NULL
                AND s.stateId = :enabledService
                AND s.dtStart IS NOT NULL AND s.dtExpires IS NOT NULL
                AND ss.isAutoRenewalEnabled = 1
                AND s.dtExpires BETWEEN :expiresFrom AND :expiresTo
                AND e.eventId IS NULL
            GROUP BY s.serviceId
            ORDER BY c.customer_id, s.companyId, s.serviceId
            ";
        } else {
            $query = "SELECT {$selectClause}
            FROM cms2_services s
            INNER JOIN (
                SELECT MAX(dtExpires) AS maxDtExpires, companyId, serviceTypeId
                FROM cms2_services
                WHERE
                    parentId IS NULL
                    AND stateId = :enabledService
                    AND dtStart IS NOT NULL AND dtExpires IS NOT NULL
                GROUP BY companyId, serviceTypeId
            ) AS latestService
            ON (
              s.dtExpires = latestService.maxDtExpires
              AND s.companyId = latestService.companyId
              AND s.serviceTypeId = latestService.serviceTypeId
            )
            INNER JOIN ch_company c ON c.company_id = s.companyId AND c.deleted = 0
            INNER JOIN cms2_customers cu ON cu.customerId = c.customer_id AND cu.deleted = 0
            INNER JOIN cms2_service_settings ss ON (ss.companyId = c.company_id AND ss.serviceTypeId = s.serviceTypeId)
            LEFT JOIN cms2_events e ON (e.objectId = s.serviceId AND eventKey = :eventKey)
            WHERE
                s.parentId IS NULL
                AND s.stateId = :enabledService
                AND s.dtStart IS NOT NULL AND s.dtExpires IS NOT NULL
                AND s.initialDuration != :plusOneMonth
                AND ss.isAutoRenewalEnabled = 1
                AND s.dtExpires BETWEEN :expiresFrom AND :expiresTo
                AND e.eventId IS NULL
            GROUP BY s.serviceId
            ORDER BY c.customer_id, s.companyId, s.serviceId
            ";
        }


        $services = $this->_em->createNativeQuery($query, $rsm)
            ->setParameter('enabledService', Service::STATE_ENABLED)
            ->setParameter('eventKey', $event)
            ->setParameter('expiresFrom', $from, Types::DATE_MUTABLE)
            ->setParameter('expiresTo', $to, Types::DATE_MUTABLE)
            ->setParameter('activeSageStatus', Token::SAGE_STATUS_ACTIVE)
            ->setParameter('plusOneMonth', Service::DURATION_ONE_MONTH)
            ->iterate();
        return $services;
    }


    /**
     * @param Date $from
     * @param Date $to
     * @param string $event
     * @return IterableResult
     */
    public function getServicesDataExpiringWithinDatesForEvent(Date $from, Date $to, $event)
    {
        $rsm = new ResultSetMappingBuilder($this->_em);
        $rsm->addRootEntityFromClassMetadata('Entities\Service', 's');
        $rsm->addJoinedEntityFromClassMetadata(
            'Entities\Company',
            'c',
            's',
            'company',
            [],
            ResultSetMappingBuilder::COLUMN_RENAMING_INCREMENT
        );

        $selectClause = $rsm->generateSelectClause(['s' => 's', 'c' => 'c']);

        $query = "SELECT {$selectClause}
            FROM cms2_services s
            INNER JOIN (
                SELECT MAX(dtExpires) AS maxDtExpires, companyId, serviceTypeId
                FROM cms2_services
                WHERE
                    parentId IS NULL
                    AND stateId = :enabledService
                    AND dtStart IS NOT NULL AND dtExpires IS NOT NULL
                GROUP BY companyId, serviceTypeId
            ) AS latestService
            ON (
              s.dtExpires = latestService.maxDtExpires
              AND s.companyId = latestService.companyId
              AND s.serviceTypeId = latestService.serviceTypeId
            )
            INNER JOIN ch_company c ON c.company_id = s.companyId AND c.deleted = 0
            INNER JOIN cms2_customers cu ON cu.customerId = c.customer_id AND cu.deleted = 0
            INNER JOIN cms2_service_settings ss ON (ss.companyId = c.company_id AND ss.serviceTypeId = s.serviceTypeId)
            INNER JOIN cms2_tokens t ON t.customerId = c.customer_id
            LEFT JOIN cms2_events e ON (e.objectId = s.serviceId AND eventKey = :eventKey)
            WHERE
                s.parentId IS NULL
                AND s.stateId = :enabledService
                AND s.dtStart IS NOT NULL AND s.dtExpires IS NOT NULL
                AND ss.isAutoRenewalEnabled = 1
                AND s.dtExpires BETWEEN :expiresFrom AND :expiresTo
                AND t.cardExpiryDate >= s.dtExpires
                AND t.sageStatus = :activeSageStatus
                AND t.isCurrent = true
                AND e.eventId IS NULL
            GROUP BY s.serviceId
            ORDER BY c.customer_id, s.companyId, s.serviceId
            ";

        $services = $this->_em->createNativeQuery($query, $rsm)
            ->setParameter('enabledService', Service::STATE_ENABLED)
            ->setParameter('eventKey', $event)
            ->setParameter('expiresFrom', $from, Types::DATE_MUTABLE)
            ->setParameter('expiresTo', $to, Types::DATE_MUTABLE)
            ->setParameter('activeSageStatus', Token::SAGE_STATUS_ACTIVE)
            ->iterate();
        return $services;
    }

    /**
     * @return IterableResult
     */
    public function getRefundedServices()
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('s')
            ->from('Entities\Service', 's')
            ->join('Entities\OrderItem', 'oi', 'WITH', 'oi.order = s.order')
            ->where('oi.productId = s.productId')
            ->andWhere('oi.isRefunded = 1')
            ->andWhere('s.parent IS NULL');
        return $qb->getQuery()->iterate();
    }

    /**
     * @param Order $order
     * @return array
     */
    public function getOrderServices(Order $order)
    {
        return $this->_em->createQueryBuilder()
            ->select('s')
            ->from('Entities\Service', 's')
            ->where('s.order = :order')
            ->andWhere('s.parent IS NULL')
            ->setParameter('order', $order)
            ->getQuery()
            ->getResult();
    }

    /**
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getLatestServiceBySettings(ServiceSettings $settings): Service
    {
        return $this->_em->createQueryBuilder()
            ->select('s')
            ->from('Entities\Service', 's')
            ->where('s.company = :company')
            ->andWhere('s.serviceTypeId = :serviceTypeId')
            ->andWhere('s.parent IS NULL')
            ->andWhere('s.stateId = :enabledState')
            ->orderBy('s.dtExpires', 'DESC')
            ->setMaxResults(1)
            ->setParameters(
                [
                    'company' => $settings->getCompany(),
                    'serviceTypeId' => $settings->getServiceTypeId(),
                    'enabledState' => Service::STATE_ENABLED,
                ]
            )
            ->getQuery()
            ->getSingleResult();
    }

    /**
     * @param Company $company
     * @param string $serviceTypeId
     * @return Service[]
     */
    public function getCompanyServicesByType(Company $company, $serviceTypeId)
    {
        return $this->_em->createQueryBuilder()
            ->select('s')
            ->from('Entities\Service', 's')
            ->where('s.company = :company')
            ->andWhere('s.serviceTypeId = :serviceTypeId')
            ->andWhere('s.parent IS NULL')
            ->andWhere('s.stateId = :enabledState')
            ->orderBy('s.dtExpires', 'DESC')
            ->setParameters(
                [
                    'company' => $company,
                    'serviceTypeId' => $serviceTypeId,
                    'enabledState' => Service::STATE_ENABLED,
                ]
            )
            ->getQuery()
            ->getResult();
    }

    /**
     * @param array $ids
     * @return Service[]
     */
    public function getServicesByIds(array $ids = [])
    {
        return $this->_em->createQueryBuilder()
            ->select('s')
            ->from($this->_entityName, 's')
            ->where('s.serviceId IN (:ids)')->setParameter('ids', $ids)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param array $dates
     * @return Service[]
     */
    public function getServicesToSendRemindersFor(array $dates): array
    {
        return $this
            ->createQueryBuilder('s')
            ->join('s.company', 'co')
            ->innerJoin(
                'Entities\ServiceSettings',
                'ss',
                Join::WITH,
                'ss.company = s.company AND ss.serviceTypeId = s.serviceTypeId AND ss.isAutoRenewalEnabled = FALSE AND ss.emailReminders = TRUE'
            )
            ->where('DATEDIFF(s.dtExpires, CURRENT_DATE()) IN (:days)')
            ->andWhere("co.companyStatus IN (:statuses)")
            ->andWhere('s.parent IS NULL')
            ->andWhere('s.renewalProductId IS NOT NULL')
            ->setParameter('days', $dates)
            ->setParameter('statuses', [Company::COMPANY_STATUS_ACTIVE, Company::COMPANY_STATUS_ACTIVE_STRIKE_OFF])
            ->getQuery()
            ->getResult();
    }

    public function hasCompanyServiceOfType(Company $company, string $serviceType): bool
    {
        return (bool)$this->getLastService($company, $serviceType);
    }

    public function getServicesByDateAndProduct(int $productId, DateTime $dtc, ?Company $company, ?int $limit)
    {
        $qb = $this
            ->createQueryBuilder('s')
            ->join('s.company', 'co')
            ->where('s.dtc >= :dtc')
            ->andWhere('s.productId = :productId')
            ->andWhere('s.stateId = :stateId')
            ->setParameter('dtc', $dtc)
            ->setParameter('productId', $productId)
            ->setParameter('stateId', Service::STATE_ENABLED);

        if ($company) {
            $qb->andWhere('s.company = :company')->setParameter('company', $company);
        }

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param string $customerId
     * @return int
     */
    public function getServiceCountByCustomerId(string $customerId): int
    {
        $services = $this
            ->createQueryBuilder('s')
            ->join('s.company', 'co')
            ->innerJoin(
                'Entities\Customer',
                'c',
                Join::WITH,
                'c.customerId = co.customer'
            )
            ->where('c.customerId = :customerId')
            ->setParameter('customerId', $customerId)
            ->andWhere('s.parent IS NULL')
            ->andWhere('CURRENT_DATE() BETWEEN s.dtStart AND s.dtExpires')
            ->getQuery()
            ->getResult();

        return count($services);
    }

    /**
     * @param string $customerId
     * @return int
     */
    public function getServiceTypeCountByCustomerId(string $customerId): int
    {
        $services = $this
            ->createQueryBuilder('s')
            ->join('s.company', 'co')
            ->innerJoin(
                'Entities\Customer',
                'c',
                Join::WITH,
                'c.customerId = co.customer'
            )
            ->where('c.customerId = :customerId')
            ->setParameter('customerId', $customerId)
            ->andWhere('s.parent IS NULL')
            ->andWhere('CURRENT_DATE() BETWEEN s.dtStart AND s.dtExpires')
            ->groupBy('s.serviceTypeId')
            ->getQuery()
            ->getResult();

        return count($services);
    }

    public function getActiveMonitoringServices(array $monitoredProductIds): array
    {
        return $this
            ->createQueryBuilder('s')
            ->innerJoin(
                Company::class,
                'co',
                Join::WITH,
                's.company = co.companyId'
            )
            ->where('CURRENT_DATE() BETWEEN s.dtStart AND s.dtExpires')
            ->andWhere('s.serviceTypeId IN (:monitoredServiceTypeIds)')
            ->andWhere('s.productId IN (:monitoredProductIds)')
            ->andWhere('co.companyNumber IS NOT NULL')
            ->setParameter('monitoredServiceTypeIds', Service::TYPE_FRAUD_PROTECTION)
            ->setParameter('monitoredProductIds', $monitoredProductIds)
            ->getQuery()
            ->getResult();
    }

    public function hasActiveMonitoringService(int $companyId, array $monitoredProductIds): bool
    {
        $services = $this
            ->createQueryBuilder('s')
            ->innerJoin(
                Company::class,
                'co',
                Join::WITH,
                's.company = co.companyId'
            )
            ->where('CURRENT_DATE() BETWEEN s.dtStart AND s.dtExpires')
            ->andWhere('s.serviceTypeId IN (:monitoredServiceTypeIds)')
            ->andWhere('s.productId IN (:monitoredProductIds)')
            ->andWhere('co.companyNumber IS NOT NULL')
            ->andWhere('co.companyId = :companyId')
            ->setParameter('monitoredServiceTypeIds', Service::TYPE_FRAUD_PROTECTION)
            ->setParameter('monitoredProductIds', $monitoredProductIds)
            ->setParameter('companyId', $companyId)
            ->getQuery()
            ->getResult();
        return !empty($services);
    }

    public function getFollowUpServices(Company $company, string $serviceTypeId): array
    {
        return $this
            ->createQueryBuilder('s')
            ->where('s.dtExpires > CURRENT_DATE()')
            ->andWhere('s.stateId = :enabled')
            ->andWhere('s.serviceTypeId  = :serviceTypeId')
            ->andWhere('s.company = :company')
            ->andWhere('s.parent IS NULL')
            ->setParameter('company', $company)
            ->setParameter('serviceTypeId', $serviceTypeId)
            ->setParameter('enabled', Service::STATE_ENABLED)
            ->getQuery()
            ->getResult();
    }

    public function getLatestServiceOfSameType(int $serviceId): Service
    {
        $service = $this->getServiceById($serviceId);

        return $this
            ->createQueryBuilder('s')
            ->where('s.serviceTypeId = :serviceTypeId')
            ->andWhere('s.company = :company')
            ->andWhere('s.parent IS NULL')
            ->setParameter('serviceTypeId', $service->getServiceTypeId())
            ->setParameter('company', $service->getCompany())
            ->orderBy('s.serviceId', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @throws QueryException
     */
    public function getFullPrivacyProductsToUpdateRenewal(int $firstYearProductId, int $secondYearProductId): iterable
    {
        $rsm = new ResultSetMappingBuilder($this->_em);
        $rsm->addRootEntityFromClassMetadata(Service::class, 's');
        $selectClause = $rsm->generateSelectClause(['s' => 's']);

        $query = "
            WITH full_privacy_going_to_next_product AS (
                SELECT 
                    companyId,
                    COUNT(*) AS qty_full_privacy,
                    MAX(dtExpires) AS last_expiration
                FROM 
                    cms2_services
                WHERE 
                    productId = :firstYearProductId
                GROUP BY 
                    companyId
                HAVING 
                    qty_full_privacy >= 11 --  it's 11 and not 12 because there's 1 initial monthly product + 11 renewals,
                ORDER BY 
                    qty_full_privacy DESC, 
                    last_expiration
            ),
            services_full_privacy AS (
                SELECT 
                    *
                FROM 
                    cms2_services
                WHERE 
                    companyId IN (SELECT companyId FROM full_privacy_going_to_next_product)
                    AND productId = :firstYearProductId
            ),
            last_full_privacy_services AS (
                SELECT 
                    sf.companyId,
                    MAX(sf.serviceId) AS last_service_monthly_privacy,
                    MAX(sf.dtExpires) AS last_dt_expires,
                    (
                        SELECT 
                            renewalProductId
                        FROM 
                            cms2_services
                        WHERE 
                            serviceId = MAX(sf.serviceId)
                    ) AS next_product_to_renew
                FROM 
                    services_full_privacy sf
                GROUP BY 
                    sf.companyId
            )
            SELECT 
                {$selectClause}
            FROM 
                last_full_privacy_services lfps
            JOIN 
                cms2_services s ON lfps.last_service_monthly_privacy = s.serviceId
            WHERE 
                lfps.last_dt_expires BETWEEN CURDATE() + INTERVAL 2 DAY AND CURDATE() + INTERVAL 30 DAY
                AND lfps.companyId NOT IN (
                    SELECT 
                        DISTINCT ce.companyId
                    FROM 
                        company_events ce
                    WHERE 
                        ce.typeId = :emailTypeId
                        AND ce.actionBy = :actionBy
                )
                AND lfps.next_product_to_renew != :secondYearProductId
            ORDER BY 
                lfps.last_dt_expires;
        ";

        return $this->_em->createNativeQuery($query, $rsm)
            ->setParameter('firstYearProductId', $firstYearProductId)
            ->setParameter('emailTypeId', CompanyEvent_Abstract::TYPE_EMAIL_SENT)
            ->setParameter('actionBy', EmailEvent::FULL_PRIVACY_PACKAGE_RENEWAL_REGULAR_PRICE)
            ->setParameter('secondYearProductId', $secondYearProductId)
            ->toIterable();
    }

    /**
     * @throws QueryException
     */
    public function getMailboxFreeTrialEligibleServices(
        array $productIds,
        bool $includeRetailers,
        bool $includeWholesalers,
        ?\DateTime $initialDate,
        ?\DateTime $endDate
    ): iterable {
        $rsm = new ResultSetMappingBuilder($this->_em);
        $rsm->addRootEntityFromClassMetadata(Service::class, 's');
        $selectClause = $rsm->generateSelectClause(['s' => 's']);

        $query = '
        SELECT ' . $selectClause . '
        FROM cms2_services s
        INNER JOIN (
            SELECT MAX(dtExpires) AS maxDtExpires, companyId, productId
            FROM cms2_services
            WHERE
                parentId IS NULL
                AND stateId = :enabledService
                AND dtStart IS NOT NULL
                AND dtExpires IS NOT NULL
            GROUP BY companyId, productId
        ) AS latestService
            ON (
                s.dtExpires = latestService.maxDtExpires
                AND s.companyId = latestService.companyId
                AND s.productId = latestService.productId
            )
        INNER JOIN ch_company c ON c.company_id = s.companyId AND c.deleted = 0
        INNER JOIN cms2_customers cu ON cu.customerId = c.customer_id AND cu.deleted = 0
        WHERE
            s.parentId IS NULL
            AND s.stateId = :enabledService
            AND s.dtStart IS NOT NULL
            AND s.dtExpires IS NOT NULL
            AND s.productId IN (:productIds)
    ';

        $query .= ($initialDate && $endDate)
            ? ' AND s.dtExpires BETWEEN :initialDate AND :endDate'
            : ' AND s.dtStart = CURDATE()';

        if ($includeRetailers xor $includeWholesalers) {
            $query .= ' AND cu.roleId = :roleId';
        } elseif (!($includeRetailers || $includeWholesalers)) {
            $query .= ' AND 1 = 0';
        }

        $query .= '
        AND NOT EXISTS (
            SELECT 1
            FROM cms2_services mailbox
            WHERE
                mailbox.companyId = c.company_id
                AND mailbox.parentId IS NULL
                AND mailbox.stateId = :enabledService
                AND mailbox.serviceTypeId IN (:excludedServiceTypes)
        )
        GROUP BY s.serviceId
        ORDER BY c.customer_id, s.companyId, s.serviceId
    ';

        $excludedServiceTypes = [Service::TYPE_MAILBOX_STANDARD, Service::TYPE_MAILBOX_PREMIUM];

        $queryObject = $this->_em->createNativeQuery($query, $rsm);
        $queryObject->setParameter('enabledService', Service::STATE_ENABLED);
        $queryObject->setParameter('productIds', $productIds);
        $queryObject->setParameter('excludedServiceTypes', $excludedServiceTypes);

        if ($includeRetailers xor $includeWholesalers) {
            $queryObject->setParameter(
                'roleId',
                $includeRetailers ? Customer::ROLE_NORMAL : Customer::ROLE_WHOLESALE
            );
        }

        if ($initialDate && $endDate) {
            $queryObject->setParameter('initialDate', $initialDate, Types::DATE_MUTABLE);
            $queryObject->setParameter('endDate', $endDate, Types::DATE_MUTABLE);
        }

        return $queryObject->toIterable();
    }
}
