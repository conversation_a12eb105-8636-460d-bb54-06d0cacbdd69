<?php

namespace BusinessServicesModule\BarclaysModule\Processors;

use BankingModule\Entities\CompanyCustomer;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use Doctrine\ORM\EntityManagerInterface;
use Entities\Company;
use Entities\Customer;
use Models\ValueObject\Country;
use Utils\Date;

class LeadProcessor
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function processLead(Lead $lead, Company $company): void
    {
        $this->entityManager->persist($lead);

        $lead->markAsProcessed();

        $this->entityManager->persist(
            $this->createCompanyCustomer($company, $lead->getPartnerServicesInformation())
        );

        $this->entityManager->persist($lead);

        $this->entityManager->flush();
    }

    private function createCompanyCustomer(Company $company, PartnerServicesInformation $psi): CompanyCustomer
    {
        if (empty($psi->getPhone())) {
            throw new \InvalidArgumentException('Phone number is required');
        }

        $companyCustomer = (new CompanyCustomer())
            ->setBankTypeId(CompanyCustomer::BANK_TYPE_BARCLAYS)
            ->setCompany($company)
            ->setWholesaler($company->getCustomer())
            ->setTitleId(array_search($psi->getTitle(), Customer::$titles, true) ?? 1)
            ->setEmail($psi->getEmail())
            ->setFirstName($psi->getFirstName())
            ->setLastName($psi->getLastName())
            ->setPhone($psi->getPhone())
            ->setAddress1($psi->getStreet())
            ->setAddress2($psi->getPremise())
            ->setCity($psi->getTown())
            ->setCounty($psi->getCounty())
            ->setPostcode($psi->getPostcode());

        if ($psi->getPhoneAlternative()) {
            $companyCustomer->setAdditionalPhone($psi->getPhoneAlternative());
        }

        if ($psi->getThoroughfare()) {
            $companyCustomer->setAddress3($psi->getThoroughfare());
        }

        if ($psi->getCounty()) {
            $companyCustomer->setCounty($psi->getCounty());
        }

        try {
            $companyCustomer->setCountryId(
                Country::fromName($psi->getCountry() ?? Country::$countriesById[Customer::UK_CITIZEN])->getId()
            );
        } catch (\Exception $e) {
            $companyCustomer->setCountryId(Customer::UK_CITIZEN);
        }

        $companyCustomer->setExistingClient($psi->isExistingClient());
        $companyCustomer->setPinSentryAccess($psi->hasPinSentryAccess());
        $companyCustomer->setOnlineSignUp($psi->isOnlineSignup());

        $companyCustomer->setPreferredContactDate(new Date());
        $companyCustomer->setConsent(CompanyCustomer::CONSENT_MY_DETAILS);

        return $companyCustomer;
    }
}
